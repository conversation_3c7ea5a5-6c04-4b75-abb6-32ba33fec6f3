import React from 'react'
import { But<PERSON>, Card, Upload, message } from 'antd'
import { UploadOutlined, FileImageOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import type { UploadProps } from 'antd'
import styles from './index.module.less'

const { Dragger } = Upload

const Materials: React.FC = () => {
  const { t } = useTranslation()

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    accept: 'image/*,video/*',
    action: '/api/upload', // 这里需要替换为实际的上传接口
    onChange(info) {
      const { status } = info.file
      if (status !== 'uploading') {
        console.log(info.file, info.fileList)
      }
      if (status === 'done') {
        message.success(`${info.file.name} 文件上传成功`)
      } else if (status === 'error') {
        message.error(`${info.file.name} 文件上传失败`)
      }
    },
    onDrop(e) {
      console.log('拖拽文件', e.dataTransfer.files)
    },
  }

  // 模拟素材数据
  const materials = [
    { id: 1, name: '产品图片1.jpg', type: 'image', size: '2.5MB' },
    { id: 2, name: '宣传视频.mp4', type: 'video', size: '15.2MB' },
    { id: 3, name: '品牌Logo.png', type: 'image', size: '1.2MB' },
    { id: 4, name: '活动海报.jpg', type: 'image', size: '3.8MB' },
  ]

  return (
    <div className={styles.materialsContainer}>
      <div className={styles.header}>
        <h1 className={styles.title}>{t('navigation.materials')}</h1>
        <Button type="primary" icon={<UploadOutlined />}>
          上传素材
        </Button>
      </div>

      <div className={styles.uploadArea}>
        <Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            <FileImageOutlined style={{ fontSize: '48px', color: '#6366f1' }} />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单个或批量上传。支持图片和视频格式。
          </p>
        </Dragger>
      </div>

      <div className={styles.materialsGrid}>
        {materials.map((material) => (
          <Card key={material.id} className={styles.materialCard} hoverable>
            <div className={styles.imageContainer}>
              <FileImageOutlined style={{ fontSize: '32px', color: '#94a3b8' }} />
            </div>
            <div className={styles.cardContent}>
              <div className={styles.fileName}>{material.name}</div>
              <div className={styles.fileInfo}>
                {material.type} • {material.size}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}

export default Materials
