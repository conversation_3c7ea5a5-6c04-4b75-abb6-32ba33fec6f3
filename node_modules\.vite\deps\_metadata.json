{"hash": "04270327", "configHash": "b208b806", "lockfileHash": "34ec99cd", "browserHash": "1d429603", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5f5259cc", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c8bc36c5", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "5e1a4be9", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "6465eb39", "needsInterop": true}, "@ant-design/icons": {"src": "../../@ant-design/icons/es/index.js", "file": "@ant-design_icons.js", "fileHash": "597b0c8a", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "ce31b945", "needsInterop": false}, "antd": {"src": "../../antd/es/index.js", "file": "antd.js", "fileHash": "e907d353", "needsInterop": false}, "antd/locale/zh_CN": {"src": "../../antd/locale/zh_CN.js", "file": "antd_locale_zh_CN.js", "fileHash": "e2e12dd0", "needsInterop": true}, "i18next": {"src": "../../i18next/dist/esm/i18next.js", "file": "i18next.js", "fileHash": "33787a8e", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c8e9af69", "needsInterop": true}, "react-i18next": {"src": "../../react-i18next/dist/es/index.js", "file": "react-i18next.js", "fileHash": "24a4d05b", "needsInterop": false}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "9fa7f829", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "901640e0", "needsInterop": false}}, "chunks": {"chunk-BVI7NZOO": {"file": "chunk-BVI7NZOO.js"}, "chunk-S2DKNLCK": {"file": "chunk-S2DKNLCK.js"}, "chunk-EQCCHGRT": {"file": "chunk-EQCCHGRT.js"}, "chunk-4HAMFFQC": {"file": "chunk-4HAMFFQC.js"}, "chunk-EQCVQC35": {"file": "chunk-EQCVQC35.js"}}}