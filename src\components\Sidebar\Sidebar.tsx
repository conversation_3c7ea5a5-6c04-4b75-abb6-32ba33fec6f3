import React from "react";
import { Layout, Menu } from "antd";
import {
  HomeOutlined,
  FileImageOutlined,
  EditOutlined,
  QrcodeOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed, onCollapse }) => {
  const { t } = useTranslation();

  const menuItems = [
    {
      key: "home",
      icon: <HomeOutlined />,
      label: t("navigation.home"),
    },
    {
      key: "materials",
      icon: <FileImageOutlined />,
      label: t("navigation.materials"),
    },
    {
      key: "copywriting",
      icon: <EditOutlined />,
      label: t("navigation.copywriting"),
    },
    {
      key: "qrcode",
      icon: <QrcodeOutlined />,
      label: t("navigation.qrCode"),
    },
  ];

  return (
    <Sider
      theme={"light"}
      collapsible
      collapsed={collapsed}
      onCollapse={onCollapse}
      width={240}
      style={{
        overflow: "auto",
        height: "100vh",
        position: "fixed",
        left: 0,
        top: 0,
        bottom: 0,
      }}
    >
      <div
        style={{
          height: "64px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          borderBottom: "1px solid var(--border-color)",
        }}
      >
        <div
          style={{
            width: "80px",
            height: "40px",
            background: "#e2e8f0",
            borderRadius: "8px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "#64748b",
            fontWeight: 500,
          }}
        >
          {t("common.logo")}
        </div>
      </div>

      <Menu
        theme="light"
        defaultSelectedKeys={["home"]}
        mode="inline"
        items={menuItems}
        style={{
          marginTop: "16px",
          border: "none",
        }}
      />
    </Sider>
  );
};

export default Sidebar;
