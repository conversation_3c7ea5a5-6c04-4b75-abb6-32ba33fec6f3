import React from 'react'
import { Layout, Dropdown, Avatar, Space } from 'antd'
import { DownOutlined, UserOutlined, GlobalOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import type { MenuProps } from 'antd'
import styles from './index.module.less'

const { Header: AntHeader } = Layout

const Header: React.FC = () => {
  const { t } = useTranslation()

  const languageItems: MenuProps['items'] = [
    {
      key: 'zh',
      label: '中文',
      icon: <GlobalOutlined />,
    },
  ]

  const userItems: MenuProps['items'] = [
    {
      key: 'profile',
      label: t('common.profile'),
      icon: <UserOutlined />,
    },
  ]

  return (
    <AntHeader className={styles.header}>
      <div className={styles.leftSection}>
        <div className={styles.logo}>
          {t('common.logo')}
        </div>
        <h1 className={styles.storeName}>
          【{t('common.storeName')}】
        </h1>
      </div>
      
      <div className={styles.rightSection}>
        <Dropdown menu={{ items: languageItems }} placement="bottomRight">
          <div className={styles.languageSelector}>
            {t('common.language')}
            <DownOutlined />
          </div>
        </Dropdown>
        
        <Dropdown menu={{ items: userItems }} placement="bottomRight">
          <Avatar 
            icon={<UserOutlined />} 
            className={styles.userAvatar}
          />
        </Dropdown>
      </div>
    </AntHeader>
  )
}

export default Header
