import React from 'react'
import { createBrowserRouter, Navigate } from 'react-router-dom'
import MainLayout from '@/components/Layout'
import Home from '@/pages/Home'
import Materials from '@/pages/Materials'
import Copywriting from '@/pages/Copywriting'
import QRCode from '@/pages/QRCode'

export const router = createBrowserRouter([
  {
    path: '/',
    element: <MainLayout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: 'materials',
        element: <Materials />,
      },
      {
        path: 'copywriting',
        element: <Copywriting />,
      },
      {
        path: 'qrcode',
        element: <QRCode />,
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to="/" replace />,
  },
])

export default router
