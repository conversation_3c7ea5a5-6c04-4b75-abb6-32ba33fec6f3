import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface UserState {
  id: string | null
  name: string
  email: string
  avatar: string
  isLoggedIn: boolean
  preferences: {
    language: string
    theme: 'light' | 'dark'
  }
}

const initialState: UserState = {
  id: null,
  name: '用户',
  email: '',
  avatar: '',
  isLoggedIn: false,
  preferences: {
    language: 'zh',
    theme: 'light',
  },
}

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<Partial<UserState>>) => {
      return { ...state, ...action.payload }
    },
    login: (state, action: PayloadAction<{ id: string; name: string; email: string }>) => {
      state.id = action.payload.id
      state.name = action.payload.name
      state.email = action.payload.email
      state.isLoggedIn = true
    },
    logout: (state) => {
      state.id = null
      state.name = '用户'
      state.email = ''
      state.isLoggedIn = false
    },
    updatePreferences: (state, action: PayloadAction<Partial<UserState['preferences']>>) => {
      state.preferences = { ...state.preferences, ...action.payload }
    },
  },
})

export const { setUser, login, logout, updatePreferences } = userSlice.actions
export default userSlice.reducer
