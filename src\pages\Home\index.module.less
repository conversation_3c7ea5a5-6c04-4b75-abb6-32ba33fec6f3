.homeContainer {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 48px;
  
  .title {
    font-size: 32px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
  }
  
  .subtitle {
    font-size: 16px;
    color: var(--text-secondary);
  }
}

.quickStartSection {
  margin-bottom: 48px;
  
  .sectionTitle {
    text-align: center;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 32px;
  }
  
  .stepsContainer {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    margin-bottom: 32px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
    }
  }
  
  .stepItem {
    flex: 1;
    text-align: center;
    
    .stepNumber {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: #e2e8f0;
      color: var(--text-secondary);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: 600;
      margin: 0 auto 16px;
    }
    
    .stepTitle {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
    }
  }
  
  .actionButtons {
    display: flex;
    justify-content: center;
    gap: 16px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
    }
  }
}

.platformsSection {
  .platformGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .platformCard {
    .cardHeader {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      
      .platformIcon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.facebook {
          background: #1877f2;
          color: white;
        }
        
        &.xiaohongshu {
          background: #ff2442;
          color: white;
        }
      }
      
      .platformTitle {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
      }
    }
    
    .connectionStatus {
      color: #ef4444;
      font-size: 14px;
      margin-bottom: 16px;
    }
    
    .configButton {
      width: 100%;
    }
  }
}
