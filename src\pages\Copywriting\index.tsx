import React, { useState } from 'react'
import { Button, Card, Input, Select, Space, message } from 'antd'
import { EditOutlined, CopyOutlined, DeleteOutlined, RobotOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import styles from './index.module.less'

const { TextArea } = Input
const { Option } = Select

const Copywriting: React.FC = () => {
  const { t } = useTranslation()
  const [generating, setGenerating] = useState(false)
  const [prompt, setPrompt] = useState('')
  const [platform, setPlatform] = useState('facebook')
  const [tone, setTone] = useState('professional')

  // 模拟文案数据
  const copywritings = [
    {
      id: 1,
      title: 'Facebook产品推广文案',
      content: '🌟 全新产品震撼上市！\n\n我们很兴奋地向大家介绍我们的最新产品，它将彻底改变您的生活方式。经过数月的精心研发，我们终于可以与大家分享这个令人惊喜的创新成果。\n\n✨ 主要特点：\n• 创新设计，简约而不简单\n• 高品质材料，经久耐用\n• 人性化功能，使用便捷\n\n现在下单还有限时优惠！不要错过这个机会！\n\n#新品上市 #品质生活 #限时优惠',
      platform: 'Facebook',
      tone: '专业',
      createdAt: '2024-01-15 10:30',
      wordCount: 156
    },
    {
      id: 2,
      title: '小红书种草文案',
      content: '姐妹们！！！这个宝藏产品我必须要分享给你们！！！\n\n用了一个月，真的是爱到不行 💕\n\n之前一直在找这种类型的产品，试过很多家都不满意，直到遇到了它！真的是一见钟情的感觉～\n\n📝 使用感受：\n✅ 质感超级好，拿在手里就很有分量\n✅ 颜值也很在线，拍照超好看\n✅ 功能很实用，完全满足日常需求\n\n真的强烈推荐给大家！现在还有活动，冲冲冲！\n\n#好物推荐 #种草 #必买清单',
      platform: '小红书',
      tone: '亲切',
      createdAt: '2024-01-14 16:45',
      wordCount: 142
    }
  ]

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      message.warning('请输入文案描述')
      return
    }

    setGenerating(true)
    // 模拟AI生成过程
    setTimeout(() => {
      setGenerating(false)
      message.success('文案生成成功！')
      // 这里应该添加新生成的文案到列表中
    }, 2000)
  }

  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content)
    message.success('文案已复制到剪贴板')
  }

  return (
    <div className={styles.copywritingContainer}>
      <div className={styles.header}>
        <h1 className={styles.title}>{t('navigation.copywriting')}</h1>
        <Button type="primary" icon={<EditOutlined />}>
          新建文案
        </Button>
      </div>

      <div className={styles.generateSection}>
        <Card className={styles.generateCard}>
          <div className={styles.cardTitle}>
            <RobotOutlined style={{ marginRight: 8, color: '#6366f1' }} />
            AI智能生成文案
          </div>
          
          <div className={styles.inputGroup}>
            <label>文案描述</label>
            <TextArea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="请描述您想要的文案内容，例如：为新款手机写一个推广文案，突出其拍照功能..."
              rows={3}
            />
          </div>

          <Space style={{ width: '100%', marginBottom: 16 }}>
            <div className={styles.inputGroup} style={{ flex: 1 }}>
              <label>目标平台</label>
              <Select value={platform} onChange={setPlatform} style={{ width: '100%' }}>
                <Option value="facebook">Facebook</Option>
                <Option value="xiaohongshu">小红书</Option>
                <Option value="weibo">微博</Option>
                <Option value="douyin">抖音</Option>
              </Select>
            </div>
            
            <div className={styles.inputGroup} style={{ flex: 1 }}>
              <label>文案风格</label>
              <Select value={tone} onChange={setTone} style={{ width: '100%' }}>
                <Option value="professional">专业</Option>
                <Option value="casual">轻松</Option>
                <Option value="friendly">亲切</Option>
                <Option value="humorous">幽默</Option>
              </Select>
            </div>
          </Space>

          <Button
            type="primary"
            size="large"
            loading={generating}
            onClick={handleGenerate}
            className={styles.generateButton}
            icon={<RobotOutlined />}
          >
            {generating ? '正在生成中...' : '生成文案'}
          </Button>
        </Card>
      </div>

      <div className={styles.copywritingList}>
        {copywritings.map((item) => (
          <Card key={item.id} className={styles.copywritingItem}>
            <div className={styles.itemHeader}>
              <div className={styles.itemTitle}>{item.title}</div>
              <div className={styles.itemActions}>
                <Button 
                  size="small" 
                  icon={<CopyOutlined />}
                  onClick={() => handleCopy(item.content)}
                >
                  复制
                </Button>
                <Button size="small" icon={<EditOutlined />}>
                  编辑
                </Button>
                <Button size="small" danger icon={<DeleteOutlined />}>
                  删除
                </Button>
              </div>
            </div>
            
            <div className={styles.itemContent}>
              {item.content}
            </div>
            
            <div className={styles.itemMeta}>
              <span>{item.platform} • {item.tone} • {item.wordCount}字</span>
              <span>{item.createdAt}</span>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}

export default Copywriting
