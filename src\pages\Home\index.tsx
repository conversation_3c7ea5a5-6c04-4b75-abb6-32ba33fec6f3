import React from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Col } from 'antd'
import { 
  UploadOutlined, 
  EditOutlined, 
  SettingOutlined,
  FacebookOutlined
} from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import styles from './index.module.less'

const Home: React.FC = () => {
  const { t } = useTranslation()

  const steps = [
    {
      number: 1,
      title: t('home.steps.uploadMaterial'),
      key: 'upload'
    },
    {
      number: 2,
      title: t('home.steps.generateCopy'),
      key: 'generate'
    },
    {
      number: 3,
      title: t('home.steps.configurePlatform'),
      key: 'configure'
    },
    {
      number: 4,
      title: t('home.steps.startPlatform'),
      key: 'start'
    }
  ]

  const platforms = [
    {
      key: 'facebook-page',
      title: t('home.platforms.facebookPage'),
      icon: 'facebook',
      connected: false
    },
    {
      key: 'facebook-share',
      title: t('home.platforms.facebookShare'),
      icon: 'facebook',
      connected: false
    },
    {
      key: 'xiaohongshu-page',
      title: t('home.platforms.xiaohongshuPage'),
      icon: 'xiaohongshu',
      connected: false
    },
    {
      key: 'xiaohongshu-share',
      title: t('home.platforms.xiaohongshuShare'),
      icon: 'xiaohongshu',
      connected: false
    }
  ]

  const renderPlatformIcon = (iconType: string) => {
    if (iconType === 'facebook') {
      return <FacebookOutlined />
    }
    // 小红书图标用红色圆圈代替
    return <div style={{ 
      width: '16px', 
      height: '16px', 
      borderRadius: '50%', 
      background: '#ff2442' 
    }} />
  }

  return (
    <div className={styles.homeContainer}>
      {/* 页面标题 */}
      <div className={styles.header}>
        <h1 className={styles.title}>{t('home.title')}</h1>
        <p className={styles.subtitle}>{t('home.subtitle')}</p>
      </div>

      {/* 快速启动区域 */}
      <div className={styles.quickStartSection}>
        <h2 className={styles.sectionTitle}>{t('home.quickStart')}</h2>
        
        {/* 步骤指示器 */}
        <div className={styles.stepsContainer}>
          {steps.map((step) => (
            <div key={step.key} className={styles.stepItem}>
              <div className={styles.stepNumber}>{step.number}</div>
              <div className={styles.stepTitle}>{step.title}</div>
            </div>
          ))}
        </div>

        {/* 操作按钮 */}
        <div className={styles.actionButtons}>
          <Button 
            type="primary" 
            size="large" 
            icon={<UploadOutlined />}
          >
            {t('home.buttons.uploadMaterial')}
          </Button>
          <Button 
            type="primary" 
            size="large" 
            icon={<EditOutlined />}
          >
            {t('home.buttons.generateCopy')}
          </Button>
        </div>
      </div>

      {/* 平台连接区域 */}
      <div className={styles.platformsSection}>
        <Row gutter={[24, 24]} className={styles.platformGrid}>
          {platforms.map((platform) => (
            <Col xs={24} lg={12} key={platform.key}>
              <Card className={styles.platformCard}>
                <div className={styles.cardHeader}>
                  <div className={`${styles.platformIcon} ${styles[platform.icon]}`}>
                    {renderPlatformIcon(platform.icon)}
                  </div>
                  <span className={styles.platformTitle}>{platform.title}</span>
                </div>
                
                <div className={styles.connectionStatus}>
                  {t('home.platforms.notConnected')}
                </div>
                
                <Button 
                  type="primary" 
                  className={styles.configButton}
                  icon={<SettingOutlined />}
                >
                  {t('home.buttons.configureContent')}
                </Button>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </div>
  )
}

export default Home
