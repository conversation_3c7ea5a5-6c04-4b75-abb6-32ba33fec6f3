import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface Copywriting {
  id: string
  title: string
  content: string
  platform: string
  tone: string
  tags: string[]
  wordCount: number
  createdAt: string
  updatedAt: string
}

interface CopywritingState {
  copywritings: Copywriting[]
  loading: boolean
  generating: boolean
  selectedCopywriting: string | null
  filters: {
    platform: string
    tone: string
    tags: string[]
    searchQuery: string
  }
}

const initialState: CopywritingState = {
  copywritings: [],
  loading: false,
  generating: false,
  selectedCopywriting: null,
  filters: {
    platform: 'all',
    tone: 'all',
    tags: [],
    searchQuery: '',
  },
}

const copywritingSlice = createSlice({
  name: 'copywriting',
  initialState,
  reducers: {
    setCopywritings: (state, action: PayloadAction<Copywriting[]>) => {
      state.copywritings = action.payload
    },
    addCopywriting: (state, action: PayloadAction<Copywriting>) => {
      state.copywritings.unshift(action.payload)
    },
    removeCopywriting: (state, action: PayloadAction<string>) => {
      state.copywritings = state.copywritings.filter(c => c.id !== action.payload)
      if (state.selectedCopywriting === action.payload) {
        state.selectedCopywriting = null
      }
    },
    updateCopywriting: (state, action: PayloadAction<{ id: string; updates: Partial<Copywriting> }>) => {
      const index = state.copywritings.findIndex(c => c.id === action.payload.id)
      if (index !== -1) {
        state.copywritings[index] = { 
          ...state.copywritings[index], 
          ...action.payload.updates,
          updatedAt: new Date().toISOString()
        }
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
    },
    setGenerating: (state, action: PayloadAction<boolean>) => {
      state.generating = action.payload
    },
    setSelectedCopywriting: (state, action: PayloadAction<string | null>) => {
      state.selectedCopywriting = action.payload
    },
    setFilters: (state, action: PayloadAction<Partial<CopywritingState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearFilters: (state) => {
      state.filters = {
        platform: 'all',
        tone: 'all',
        tags: [],
        searchQuery: '',
      }
    },
  },
})

export const {
  setCopywritings,
  addCopywriting,
  removeCopywriting,
  updateCopywriting,
  setLoading,
  setGenerating,
  setSelectedCopywriting,
  setFilters,
  clearFilters,
} = copywritingSlice.actions

export default copywritingSlice.reducer
