import React, { useState } from 'react'
import { But<PERSON>, Card, Input, Select, ColorPicker, message } from 'antd'
import { QrcodeOutlined, DownloadOutlined, DeleteOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import styles from './index.module.less'

const { Option } = Select

const QRCode: React.FC = () => {
  const { t } = useTranslation()
  const [generating, setGenerating] = useState(false)
  const [url, setUrl] = useState('')
  const [size, setSize] = useState(200)
  const [color, setColor] = useState('#000000')

  // 模拟二维码数据
  const qrcodes = [
    {
      id: 1,
      title: '官网链接',
      url: 'https://www.example.com',
      size: 200,
      color: '#000000',
      createdAt: '2024-01-15 14:20'
    },
    {
      id: 2,
      title: 'Facebook页面',
      url: 'https://facebook.com/yourpage',
      size: 200,
      color: '#1877f2',
      createdAt: '2024-01-14 09:15'
    },
    {
      id: 3,
      title: '产品详情页',
      url: 'https://shop.example.com/product/123',
      size: 200,
      color: '#000000',
      createdAt: '2024-01-13 16:30'
    }
  ]

  const handleGenerate = async () => {
    if (!url.trim()) {
      message.warning('请输入要生成二维码的链接')
      return
    }

    // 简单的URL验证
    try {
      new URL(url)
    } catch {
      message.error('请输入有效的URL地址')
      return
    }

    setGenerating(true)
    // 模拟生成过程
    setTimeout(() => {
      setGenerating(false)
      message.success('二维码生成成功！')
      // 这里应该添加新生成的二维码到列表中
    }, 1000)
  }

  const handleDownload = (qrcode: any) => {
    // 这里应该实现下载功能
    message.success(`正在下载 ${qrcode.title} 的二维码`)
  }

  const handleDelete = (id: number) => {
    // 这里应该实现删除功能
    message.success('二维码已删除')
  }

  return (
    <div className={styles.qrcodeContainer}>
      <div className={styles.header}>
        <h1 className={styles.title}>{t('navigation.qrCode')}</h1>
      </div>

      <div className={styles.generateSection}>
        <Card className={styles.generateCard}>
          <div className={styles.cardTitle}>
            <QrcodeOutlined style={{ marginRight: 8, color: '#6366f1' }} />
            生成二维码
          </div>
          
          <div className={styles.formRow}>
            <div className={styles.formItem}>
              <label>链接地址 *</label>
              <Input
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="请输入要生成二维码的链接，例如：https://www.example.com"
              />
            </div>
          </div>

          <div className={styles.formRow}>
            <div className={styles.formItem}>
              <label>二维码尺寸</label>
              <Select value={size} onChange={setSize} style={{ width: '100%' }}>
                <Option value={150}>150x150</Option>
                <Option value={200}>200x200</Option>
                <Option value={300}>300x300</Option>
                <Option value={400}>400x400</Option>
              </Select>
            </div>
            
            <div className={styles.formItem}>
              <label>二维码颜色</label>
              <ColorPicker
                value={color}
                onChange={(_, hex) => setColor(hex)}
                style={{ width: '100%' }}
              />
            </div>
          </div>

          <Button
            type="primary"
            size="large"
            loading={generating}
            onClick={handleGenerate}
            className={styles.generateButton}
            icon={<QrcodeOutlined />}
          >
            {generating ? '正在生成中...' : '生成二维码'}
          </Button>
        </Card>
      </div>

      <div className={styles.qrcodeList}>
        {qrcodes.map((qrcode) => (
          <Card key={qrcode.id} className={styles.qrcodeItem}>
            <div className={styles.qrcodePreview}>
              <div className={styles.placeholder}>
                二维码预览
                <br />
                {qrcode.size}x{qrcode.size}
              </div>
            </div>
            
            <div className={styles.qrcodeInfo}>
              <div className={styles.qrcodeTitle}>{qrcode.title}</div>
              <div className={styles.qrcodeUrl}>{qrcode.url}</div>
              
              <div className={styles.qrcodeActions}>
                <Button 
                  size="small" 
                  icon={<DownloadOutlined />}
                  onClick={() => handleDownload(qrcode)}
                >
                  下载
                </Button>
                <Button 
                  size="small" 
                  danger 
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(qrcode.id)}
                >
                  删除
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}

export default QRCode
