import React, { useState } from 'react'
import { Layout } from 'antd'
import { Outlet } from 'react-router-dom'
import Header from '../Header'
import Sidebar from '../Sidebar'
import styles from './index.module.less'

const { Content } = Layout

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false)

  return (
    <Layout className={styles.layout}>
      <Sidebar collapsed={collapsed} onCollapse={setCollapsed} />
      <Layout 
        className={styles.contentLayout}
        style={{ marginLeft: collapsed ? 80 : 240 }}
      >
        <Header />
        <Content className={styles.content}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  )
}

export default MainLayout
