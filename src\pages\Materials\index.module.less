.materialsContainer {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }
}

.uploadArea {
  margin-bottom: 24px;
  
  :global(.ant-upload-drag) {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    background: #fafbfc;
    
    &:hover {
      border-color: var(--primary-color);
    }
  }
}

.materialsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.materialCard {
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  .imageContainer {
    width: 100%;
    height: 150px;
    background: #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .cardContent {
    padding: 12px;
    
    .fileName {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .fileInfo {
      font-size: 12px;
      color: var(--text-secondary);
    }
  }
}
