import React from "react";
import { Layout, Menu } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import {
  HomeOutlined,
  FileImageOutlined,
  EditOutlined,
  QrcodeOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { setSidebarCollapsed } from "@/store/slices/appSlice";
import styles from "./index.module.less";

const { Sider } = Layout;

const Sidebar: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { sidebarCollapsed } = useAppSelector((state) => state.app);

  const menuItems = [
    {
      key: "/",
      icon: <HomeOutlined />,
      label: t("navigation.home"),
    },
    {
      key: "/materials",
      icon: <FileImageOutlined />,
      label: t("navigation.materials"),
    },
    {
      key: "/copywriting",
      icon: <EditOutlined />,
      label: t("navigation.copywriting"),
    },
    {
      key: "/qrcode",
      icon: <QrcodeOutlined />,
      label: t("navigation.qrCode"),
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Sider
      collapsible
      collapsed={sidebarCollapsed}
      onCollapse={(collapsed) => dispatch(setSidebarCollapsed(collapsed))}
      width={240}
      className={styles.sidebar}
    >
      <div className={styles.logoSection}>
        {!collapsed && <span className={styles.logoText}>社交分享平台</span>}
      </div>

      <div className={styles.menuContainer}>
        <Menu
          theme="light"
          selectedKeys={[location.pathname]}
          mode="inline"
          items={menuItems}
          onClick={handleMenuClick}
        />
      </div>
    </Sider>
  );
};

export default Sidebar;
