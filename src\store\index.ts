import { configureStore } from '@reduxjs/toolkit'
import userSlice from './slices/userSlice'
import appSlice from './slices/appSlice'
import materialsSlice from './slices/materialsSlice'
import copywritingSlice from './slices/copywritingSlice'

export const store = configureStore({
  reducer: {
    user: userSlice,
    app: appSlice,
    materials: materialsSlice,
    copywriting: copywritingSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
