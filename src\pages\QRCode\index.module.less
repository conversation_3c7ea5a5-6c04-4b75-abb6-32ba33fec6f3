.qrcodeContainer {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }
}

.generateSection {
  margin-bottom: 32px;
  
  .generateCard {
    .cardTitle {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 16px;
    }
    
    .formRow {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 12px;
      }
      
      .formItem {
        flex: 1;
        
        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: var(--text-primary);
        }
      }
    }
    
    .generateButton {
      width: 100%;
    }
  }
}

.qrcodeList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  
  .qrcodeItem {
    text-align: center;
    
    .qrcodePreview {
      width: 200px;
      height: 200px;
      margin: 0 auto 16px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8fafc;
      
      .placeholder {
        color: var(--text-secondary);
        font-size: 14px;
      }
    }
    
    .qrcodeInfo {
      .qrcodeTitle {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 8px;
      }
      
      .qrcodeUrl {
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: 12px;
        word-break: break-all;
      }
      
      .qrcodeActions {
        display: flex;
        justify-content: center;
        gap: 8px;
      }
    }
  }
}
