.copywritingContainer {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }
}

.generateSection {
  margin-bottom: 32px;
  
  .generateCard {
    .cardTitle {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 16px;
    }
    
    .inputGroup {
      margin-bottom: 16px;
      
      label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
      }
    }
    
    .generateButton {
      width: 100%;
    }
  }
}

.copywritingList {
  .copywritingItem {
    margin-bottom: 16px;
    
    .itemHeader {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-bottom: 12px;
      
      .itemTitle {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
        flex: 1;
      }
      
      .itemActions {
        display: flex;
        gap: 8px;
      }
    }
    
    .itemContent {
      background: #f8fafc;
      padding: 16px;
      border-radius: 8px;
      border-left: 4px solid var(--primary-color);
      line-height: 1.6;
      color: var(--text-primary);
    }
    
    .itemMeta {
      margin-top: 12px;
      font-size: 12px;
      color: var(--text-secondary);
      display: flex;
      justify-content: space-between;
    }
  }
}
