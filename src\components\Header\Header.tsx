import React from "react";
import { Layout, Dropdown, Avatar, Space } from "antd";
import { DownOutlined, UserOutlined, GlobalOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import type { MenuProps } from "antd";

const { Header: AntHeader } = Layout;

const Header: React.FC = () => {
  const { t } = useTranslation();

  const languageItems: MenuProps["items"] = [
    {
      key: "zh",
      label: "中文",
      icon: <GlobalOutlined />,
    },
  ];

  const userItems: MenuProps["items"] = [
    {
      key: "profile",
      label: t("common.profile"),
      icon: <UserOutlined />,
    },
  ];

  return (
    <AntHeader
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "0 24px",
      }}
    >
      <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
        <h1
          style={{
            margin: 0,
            fontSize: "20px",
            fontWeight: 600,
            color: "var(--text-primary)",
          }}
        >
          【{t("common.storeName")}】
        </h1>
      </div>

      <Space size="large">
        <Dropdown menu={{ items: languageItems }} placement="bottomRight">
          <Space style={{ cursor: "pointer" }}>
            {t("common.language")}
            <DownOutlined />
          </Space>
        </Dropdown>

        <Dropdown menu={{ items: userItems }} placement="bottomRight">
          <Avatar icon={<UserOutlined />} style={{ cursor: "pointer" }} />
        </Dropdown>
      </Space>
    </AntHeader>
  );
};

export default Header;
