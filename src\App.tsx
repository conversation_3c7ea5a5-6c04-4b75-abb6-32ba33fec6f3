import React from 'react'
import { ConfigProvider } from 'antd'
import { Provider } from 'react-redux'
import { RouterProvider } from 'react-router-dom'
import zhCN from 'antd/locale/zh_CN'
import { store } from './store'
import router from './router'
import './i18n'
import './styles/antd-override.less'

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider 
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#6366f1',
            borderRadius: 8,
            colorBgContainer: '#ffffff',
          },
        }}
      >
        <RouterProvider router={router} />
      </ConfigProvider>
    </Provider>
  )
}

export default App
