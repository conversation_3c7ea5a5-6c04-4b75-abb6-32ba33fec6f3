import React from "react";
import { ConfigProvider } from "antd";
import zhCN from "antd/locale/zh_CN";
import MainLayout from "./components/Layout/MainLayout";
import Home from "./pages/Home/Home";
import "./i18n";
import "./styles/antd-override.less";

const App: React.FC = () => {
  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: "#6186EE",
          borderRadius: 8,
          colorBgContainer: "#ffffff",
        },
      }}
    >
      <MainLayout>
        <Home />
      </MainLayout>
    </ConfigProvider>
  );
};

export default App;
