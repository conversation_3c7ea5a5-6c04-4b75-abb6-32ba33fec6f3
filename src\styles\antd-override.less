// Antd 样式覆盖
// 注意：Antd 5.x 不再需要手动导入样式文件，组件会自动按需加载样式

// 全局变量
:root {
  --primary-color: #6186EE;
  --primary-hover: #4096ff;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

// 全局样式重置
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
}

// Layout 覆盖
.ant-layout {
  background: var(--background-color);
  min-height: 100vh;
}

.ant-layout-header {
  background: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
}

.ant-layout-sider {
  background: var(--card-background);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.ant-layout-content {
  padding: 24px;
  background: var(--background-color);
}

// Menu 覆盖
.ant-menu {
  background: transparent;
  border: none;
}

.ant-menu-item {
  border-radius: 8px;
  margin: 4px 8px;
  width: calc(100% - 16px);
  
  &:hover {
    background-color: #f1f5f9;
  }
  
  &.ant-menu-item-selected {
    background-color: var(--primary-color);
    color: white;
    
    .ant-menu-item-icon {
      color: white;
    }
  }
}

// Button 覆盖
.ant-btn {
  border-radius: 8px;
  font-weight: 500;
  // box-shadow: var(--shadow-sm);
  
  &.ant-btn-primary {
    // background: var(--primary-color);
    // border-color: var(--primary-color);
    
    &:hover {
      // background: var(--primary-hover);
      // border-color: var(--primary-hover);
    }
  }
}

// Card 覆盖
.ant-card {
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  
  .ant-card-head {
    border-bottom: 1px solid var(--border-color);
  }
}

// Dropdown 覆盖
.ant-dropdown {
  .ant-dropdown-menu {
    border-radius: 8px;
    box-shadow: var(--shadow-md);
  }
}
