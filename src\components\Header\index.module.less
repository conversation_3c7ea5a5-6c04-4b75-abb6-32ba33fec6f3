.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  height: 64px;
}

.leftSection {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo {
  width: 80px;
  height: 40px;
  background: #e2e8f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-weight: 500;
  font-size: 14px;
}

.storeName {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.rightSection {
  display: flex;
  align-items: center;
  gap: 24px;
}

.languageSelector {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 14px;
  
  &:hover {
    color: var(--primary-color);
  }
}

.userAvatar {
  cursor: pointer;
  
  &:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
  }
}
