.sidebar {
  overflow: auto;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  background: var(--card-background);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.logoSection {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--border-color);
  padding: 0 16px;
}

.logoText {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menuContainer {
  margin-top: 16px;
  
  :global(.ant-menu) {
    background: transparent;
    border: none;
  }
  
  :global(.ant-menu-item) {
    border-radius: 8px;
    margin: 4px 8px;
    width: calc(100% - 16px);
    
    &:hover {
      background-color: #f1f5f9;
    }
    
    &.ant-menu-item-selected {
      background-color: var(--primary-color);
      color: white;
      
      .ant-menu-item-icon {
        color: white;
      }
    }
  }
  
  :global(.ant-menu-item-icon) {
    font-size: 16px;
  }
}
